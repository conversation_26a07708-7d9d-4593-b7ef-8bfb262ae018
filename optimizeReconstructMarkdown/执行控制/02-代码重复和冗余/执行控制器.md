# 🎮 执行控制器 - 代码重复和冗余

## 🎯 使用说明

**这是代码重复和冗余问题分类的主执行控制器。将此文件发送给AI，AI会根据当前状态自动执行相应的重构步骤。**

## 📋 当前执行状态

### 🔄 当前阶段：⏳ 代码重复和冗余处理

### 📍 当前步骤：准备开始

### 🎯 下一步行动：开始组件重复问题处理

## 🗂️ 子步骤概览

### ⏳ 01-组件重复问题

- **状态**: 待开始
- **描述**: 识别和处理重复的组件代码
- **文档**: `./01-组件重复问题/执行控制器.md`
- **预计时间**: 2-3天
- **风险等级**: 🟡 中等

### ⏳ 02-逻辑重复问题

- **状态**: 待开始
- **描述**: 提取和重构重复的业务逻辑
- **文档**: `./02-逻辑重复问题/执行控制器.md`
- **预计时间**: 2-3天
- **风险等级**: 🟡 中等

## 🔄 执行流程规范

### ⚠️ 重要：标准执行流程

每个子步骤必须严格按照以下顺序执行：

1. **📝 分析重复代码**

    - 识别重复的组件或逻辑
    - 分析重复的原因和影响

2. **🔧 设计重构方案**

    - 制定代码提取和重构策略
    - 确定共享组件或工具函数的设计

3. **💻 执行代码重构**

    - 提取重复代码到共享位置
    - 更新所有引用位置

4. **🧪 运行项目测试**

    ```bash
    # 启动开发服务器
    pnpm dev

    # 检查编译状态
    npx tsc --noEmit
    ```

5. **✅ 确认功能正常**

    - 验证重构后功能完整性
    - 确保没有引入新的问题

6. **📦 执行Git提交**
    ```bash
    git add .
    git commit -m "refactor: 处理[具体重复问题]"
    ```

## 🚀 执行指令区域

**请在下面选择你要执行的操作（取消注释相应行）：**

```bash
# ===== 🚀 开始执行 =====
# 取消下面一行的注释来开始第一个子步骤
EXECUTE: 01-组件重复问题
# EXECUTE: 02-逻辑重复问题

# ===== 🔄 继续执行 =====
# 如果中途中断，取消下面相应行的注释来继续
# CONTINUE: 01-组件重复问题
# CONTINUE: 02-逻辑重复问题

# ===== 🚨 回滚操作 =====
# 如果需要回滚，取消下面相应行的注释
# ROLLBACK: 回滚到上一个子步骤
# ROLLBACK: 回滚到阶段开始前

# ===== ✅ 完成确认 =====
# 子步骤完成后，取消下面相应行的注释
# COMPLETED: 01-组件重复问题
# COMPLETED: 02-逻辑重复问题

# ===== 🔍 状态检查 =====
# 检查当前状态，取消下面一行的注释
# CHECK: 代码重复和冗余状态检查
```

## 📊 执行历史记录

### 执行日志

```
2025-08-01 - 代码重复和冗余阶段准备开始
⏳ 等待开始: 01-组件重复问题
⏳ 等待开始: 02-逻辑重复问题
```

### 预期成果

- **组件复用**: 提取重复组件到共享位置
- **逻辑复用**: 创建通用工具函数和hooks
- **代码减少**: 显著减少重复代码量
- **维护性提升**: 统一的组件和逻辑更易维护

## 🎯 重构目标

### 主要问题

1. **组件重复**: 多个相似的组件实现
2. **逻辑重复**: 重复的业务逻辑代码
3. **工具函数重复**: 相同功能的工具函数散布各处

### 解决方案

1. **组件抽象**: 提取通用组件到shared/components
2. **逻辑抽象**: 创建通用hooks和工具函数
3. **统一接口**: 建立一致的组件和函数接口

## 🔧 配置信息

### 项目路径

- **项目根目录**: `/Users/<USER>/Desktop/岸边/next_wallpaper/`
- **共享组件目录**: `./app/shared/components/`
- **共享工具目录**: `./app/shared/utils/`
- **共享hooks目录**: `./app/shared/hooks/`

### 执行参数

- **重构策略**: 渐进式重构，保持功能完整性
- **验证模式**: 每个重构完成后立即验证
- **安全模式**: 启用Git检查点
- **排除项**: 保持平台差异化实现（Mobile/PC）

## 🚫 明确排除的内容

**以下内容不会被处理：**

1. **Mobile/PC代码重复** - 保持平台差异化实现
2. **CSS样式重复** - 保持现有样式组织方式
3. **第三方库重复引用** - 保持现有引用方式

## 🤖 AI执行逻辑

当你将此文件发送给AI时，AI会：

1. **解析执行指令**: 检查执行指令区域
2. **确定当前状态**: 根据执行历史判断当前进度
3. **执行相应操作**:
    - `EXECUTE`: 开始执行指定子步骤
    - `CONTINUE`: 继续执行中断的子步骤
    - `ROLLBACK`: 执行回滚操作
    - `CHECK`: 检查当前状态
4. **更新执行记录**: 自动更新执行历史
5. **返回执行结果**: 提供详细报告和下一步建议

---

**💡 提示：准备开始代码重复和冗余处理，建议先从组件重复问题开始！**
